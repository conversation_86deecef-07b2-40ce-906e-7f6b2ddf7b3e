'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { HelpCircle } from 'lucide-react';

export default function HelpPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Help & Support"
        description="Access documentation, tutorials, and support resources to get the most out of SynapseAI."
        features={[
          "Comprehensive user documentation and guides",
          "Video tutorials and getting started walkthroughs",
          "API documentation and developer resources",
          "Community forum and knowledge base",
          "Live chat support and ticket system",
          "FAQ and troubleshooting guides"
        ]}
        estimatedDate="Q1 2024"
        icon={<HelpCircle className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
