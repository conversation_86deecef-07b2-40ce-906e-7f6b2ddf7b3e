'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Bot } from 'lucide-react';

export default function AgentsPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="AI Agents"
        description="Browse, manage, and deploy your AI agents. View performance metrics and configure agent settings."
        features={[
          "View all created AI agents in a comprehensive dashboard",
          "Monitor agent performance and usage statistics",
          "Quick deploy and manage agent instances",
          "Filter and search agents by category, status, or tags",
          "Bulk operations for agent management",
          "Agent health monitoring and alerts"
        ]}
        estimatedDate="Q1 2024"
        icon={<Bot className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
