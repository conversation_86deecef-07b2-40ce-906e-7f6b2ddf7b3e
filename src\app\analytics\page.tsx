'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { BarChart3 } from 'lucide-react';

export default function AnalyticsPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Analytics Overview"
        description="Comprehensive analytics dashboard providing insights into your AI agents, workflows, and system performance."
        features={[
          "Real-time dashboard with key performance metrics",
          "Agent usage and performance analytics",
          "Workflow execution statistics and success rates",
          "Cost analysis and resource utilization tracking",
          "Custom reports and data visualization",
          "Export capabilities for external analysis"
        ]}
        estimatedDate="Q2 2024"
        icon={<BarChart3 className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
