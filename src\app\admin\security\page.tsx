'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Shield } from 'lucide-react';

export default function AdminSecurityPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Security Management"
        description="Configure security settings, access controls, and compliance features for your organization."
        features={[
          "Multi-factor authentication configuration",
          "Single Sign-On (SSO) integration setup",
          "Access control policies and permissions",
          "Security audit logs and monitoring",
          "Data encryption and privacy settings",
          "Compliance reporting and certifications"
        ]}
        estimatedDate="Q1 2024"
        icon={<Shield className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
