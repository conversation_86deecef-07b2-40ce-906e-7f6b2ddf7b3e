'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { FileText } from 'lucide-react';

export default function KnowledgeBasePage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Knowledge Base"
        description="Centralize and manage your organization's knowledge to power intelligent AI agents with contextual information."
        features={[
          "Upload and organize documents, PDFs, and text files",
          "Automatic content indexing and semantic search",
          "Knowledge graph visualization and relationships",
          "Version control for knowledge base updates",
          "Access control and permission management",
          "Integration with popular document management systems"
        ]}
        estimatedDate="Q1 2024"
        icon={<FileText className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
