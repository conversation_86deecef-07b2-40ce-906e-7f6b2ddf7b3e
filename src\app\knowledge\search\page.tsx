'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { FileText } from 'lucide-react';

export default function KnowledgeSearchPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Knowledge Search"
        description="Powerful semantic search across your entire knowledge base with AI-powered insights and contextual results."
        features={[
          "Semantic search with natural language queries",
          "Advanced filtering by document type, date, and tags",
          "Search result highlighting and context snippets",
          "Saved searches and search history",
          "Search analytics and popular queries",
          "AI-powered search suggestions and auto-complete"
        ]}
        estimatedDate="Q1 2024"
        icon={<FileText className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/knowledge"
      />
    </AdminLayout>
  );
}
