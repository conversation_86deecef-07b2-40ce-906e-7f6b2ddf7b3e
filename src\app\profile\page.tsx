'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { User } from 'lucide-react';

export default function ProfilePage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="User Profile"
        description="Manage your personal information, preferences, and account settings."
        features={[
          "Edit personal information and contact details",
          "Update profile picture and display preferences",
          "Manage notification settings",
          "View account activity and login history",
          "Configure privacy and security settings",
          "Export personal data and account information"
        ]}
        estimatedDate="Q2 2024"
        icon={<User className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
