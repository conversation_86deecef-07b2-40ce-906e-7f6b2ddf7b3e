'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { FileText } from 'lucide-react';

export default function KnowledgeUploadPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Document Upload"
        description="Upload and process documents to expand your knowledge base with drag-and-drop simplicity and batch processing."
        features={[
          "Drag-and-drop file upload interface",
          "Batch upload for multiple documents",
          "Support for PDF, Word, Excel, PowerPoint, and text files",
          "Automatic text extraction and processing",
          "Upload progress tracking and error handling",
          "Integration with cloud storage providers"
        ]}
        estimatedDate="Q1 2024"
        icon={<FileText className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/knowledge"
      />
    </AdminLayout>
  );
}
