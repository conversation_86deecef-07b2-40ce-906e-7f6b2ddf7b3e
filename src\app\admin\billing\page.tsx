'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { CreditCard } from 'lucide-react';

export default function AdminBillingPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Billing Management"
        description="Manage subscription plans, billing information, and payment methods for your organization."
        features={[
          "View current subscription plan and usage limits",
          "Manage payment methods and billing information",
          "Download invoices and billing history",
          "Usage tracking and cost analysis",
          "Plan upgrade and downgrade options",
          "Team billing and cost allocation"
        ]}
        estimatedDate="Q2 2024"
        icon={<CreditCard className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
