'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Database } from 'lucide-react';

export default function AdminSystemPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="System Administration"
        description="Monitor system health, manage infrastructure, and configure advanced system settings."
        features={[
          "System health monitoring and alerts",
          "Database management and backup configuration",
          "Server resource monitoring and scaling",
          "System logs and error tracking",
          "Performance optimization and tuning",
          "Maintenance scheduling and updates"
        ]}
        estimatedDate="Q2 2024"
        icon={<Database className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
