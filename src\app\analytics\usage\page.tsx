'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { BarChart3 } from 'lucide-react';

export default function AnalyticsUsagePage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Usage Analytics"
        description="Track and analyze usage patterns, user behavior, and feature adoption across your AI platform."
        features={[
          "User activity tracking and engagement metrics",
          "Feature usage statistics and adoption rates",
          "Agent and workflow utilization patterns",
          "Time-based usage analysis and trends",
          "User segmentation and behavior insights",
          "Usage forecasting and capacity planning"
        ]}
        estimatedDate="Q2 2024"
        icon={<BarChart3 className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/analytics"
      />
    </AdminLayout>
  );
}
