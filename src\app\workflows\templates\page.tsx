'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { GitBranch } from 'lucide-react';

export default function WorkflowTemplatesPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Workflow Templates"
        description="Get started quickly with pre-built workflow templates for common automation scenarios and business processes."
        features={[
          "Ready-to-use workflow templates for various industries",
          "Customizable templates with drag-and-drop editing",
          "Template categories: Data Processing, Customer Service, Content Creation",
          "Import/export templates for team collaboration",
          "Template versioning and change tracking",
          "Community template sharing and contributions"
        ]}
        estimatedDate="Q2 2024"
        icon={<GitBranch className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/workflows"
      />
    </AdminLayout>
  );
}
