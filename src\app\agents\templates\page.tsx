'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Bo<PERSON> } from 'lucide-react';

export default function AgentTemplatesPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Agent Templates"
        description="Discover and use pre-built agent templates to quickly create powerful AI agents for common use cases."
        features={[
          "Browse curated collection of agent templates",
          "Filter templates by industry, use case, or complexity",
          "Preview template configurations and capabilities",
          "One-click deployment from templates",
          "Customize templates before deployment",
          "Community-contributed templates marketplace"
        ]}
        estimatedDate="Q2 2024"
        icon={<Bot className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/agents"
      />
    </AdminLayout>
  );
}
