'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { FileText } from 'lucide-react';

export default function KnowledgeDocumentsPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Document Management"
        description="Manage your knowledge base documents with advanced organization, tagging, and version control features."
        features={[
          "Document library with folder organization",
          "Advanced tagging and categorization system",
          "Document preview and annotation tools",
          "Collaborative editing and review workflows",
          "Document analytics and usage tracking",
          "Bulk operations for document management"
        ]}
        estimatedDate="Q1 2024"
        icon={<FileText className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/knowledge"
      />
    </AdminLayout>
  );
}
