'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Wrench } from 'lucide-react';

export default function ToolsMarketplacePage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Tools Marketplace"
        description="Discover, install, and manage tools from the community marketplace to extend your AI agents' capabilities."
        features={[
          "Browse thousands of community-created tools",
          "Filter tools by category, rating, and compatibility",
          "One-click installation and configuration",
          "Tool reviews and ratings system",
          "Version management and automatic updates",
          "Publish your own tools to the marketplace"
        ]}
        estimatedDate="Q2 2024"
        icon={<Wrench className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/tools"
      />
    </AdminLayout>
  );
}
