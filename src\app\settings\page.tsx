'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Settings } from 'lucide-react';

export default function SettingsPage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Application Settings"
        description="Configure your application preferences, integrations, and workspace settings."
        features={[
          "Customize dashboard layout and widgets",
          "Configure notification preferences",
          "Manage API keys and integrations",
          "Set up workspace themes and appearance",
          "Configure default agent and workflow settings",
          "Manage data retention and backup preferences"
        ]}
        estimatedDate="Q2 2024"
        icon={<Settings className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/dashboard"
      />
    </AdminLayout>
  );
}
