'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Construction, 
  Clock, 
  ArrowLeft, 
  Bell, 
  Lightbulb,
  Rocket,
  Star
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface ComingSoonProps {
  title: string;
  description?: string;
  features?: string[];
  estimatedDate?: string;
  showBackButton?: boolean;
  backUrl?: string;
  className?: string;
  icon?: React.ReactNode;
  variant?: 'default' | 'minimal' | 'detailed';
}

export function ComingSoon({
  title,
  description = "This feature is currently under development and will be available soon.",
  features = [],
  estimatedDate,
  showBackButton = true,
  backUrl,
  className,
  icon,
  variant = 'default'
}: ComingSoonProps) {
  const router = useRouter();

  const handleBack = () => {
    if (backUrl) {
      router.push(backUrl);
    } else {
      router.back();
    }
  };

  const handleNotifyMe = () => {
    // TODO: Implement notification subscription
    console.log('Notify me when available:', title);
  };

  if (variant === 'minimal') {
    return (
      <div className={cn("flex flex-col items-center justify-center min-h-[400px] text-center", className)}>
        <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
          {icon || <Construction className="w-8 h-8 text-muted-foreground" />}
        </div>
        <h2 className="text-2xl font-semibold mb-2">{title}</h2>
        <p className="text-muted-foreground mb-6 max-w-md">{description}</p>
        {showBackButton && (
          <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
            <ArrowLeft className="w-4 h-4" />
            Go Back
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={cn("container mx-auto px-4 py-8", className)}>
      <div className="max-w-2xl mx-auto">
        <Card className="border-dashed border-2">
          <CardHeader className="text-center pb-4">
            <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/20 dark:to-purple-900/20 rounded-full flex items-center justify-center">
              {icon || <Construction className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
            </div>
            <CardTitle className="text-3xl font-bold mb-2">{title}</CardTitle>
            <CardDescription className="text-lg">{description}</CardDescription>
            
            {estimatedDate && (
              <div className="flex items-center justify-center gap-2 mt-4">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <Badge variant="secondary" className="text-sm">
                  Expected: {estimatedDate}
                </Badge>
              </div>
            )}
          </CardHeader>

          <CardContent className="space-y-6">
            {features.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
                  <Lightbulb className="w-5 h-5 text-yellow-500" />
                  Planned Features
                </h3>
                <ul className="space-y-2">
                  {features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-3 text-sm">
                      <Star className="w-4 h-4 text-yellow-500 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              {showBackButton && (
                <Button variant="outline" onClick={handleBack} className="flex items-center gap-2">
                  <ArrowLeft className="w-4 h-4" />
                  Go Back
                </Button>
              )}
              
              <Button onClick={handleNotifyMe} className="flex items-center gap-2">
                <Bell className="w-4 h-4" />
                Notify Me When Available
              </Button>
              
              <Button variant="secondary" className="flex items-center gap-2">
                <Rocket className="w-4 h-4" />
                View Roadmap
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
