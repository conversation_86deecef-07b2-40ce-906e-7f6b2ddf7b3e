'use client';

import React from 'react';
import AdminLayout from '@/components/layout/AdminLayout';
import { ComingSoon } from '@/components/ui/coming-soon';
import { Palette } from 'lucide-react';

export default function WidgetsMarketplacePage() {
  return (
    <AdminLayout>
      <ComingSoon
        title="Widgets Marketplace"
        description="Discover and install dashboard widgets from the community to enhance your workspace with custom visualizations and tools."
        features={[
          "Browse community-created dashboard widgets",
          "Filter widgets by category, rating, and functionality",
          "Preview widgets before installation",
          "One-click installation and configuration",
          "Widget reviews and ratings system",
          "Publish your own widgets to the marketplace"
        ]}
        estimatedDate="Q3 2024"
        icon={<Palette className="w-10 h-10 text-blue-600 dark:text-blue-400" />}
        backUrl="/widgets"
      />
    </AdminLayout>
  );
}
